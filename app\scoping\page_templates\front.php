<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry bg-front" style="">
    <style>
        .bg-front {
            background-image:url('page_templates/front.jpg');
            background-size: cover;
            color: #FFF;
        }
    </style>
    <div class="page-content">
        <h1>Deckblatt</h1>
        <?php if (!empty($projectData) && !empty($projectData['info_data']['name'])): ?>
            <h2>Projekt: <?php echo htmlspecialchars($projectData['info_data']['name']); ?></h2>
        <?php endif; ?>

        <?php if (!empty($projectData) && !empty($projectData['info_data']['city'])): ?>
            <p><strong>Standort:</strong> <?php echo htmlspecialchars($projectData['info_data']['city']); ?></p>
        <?php endif; ?>

        <p><strong>Erstellt am:</strong> <?php echo htmlspecialchars($scopingData['created_at']); ?></p>
    </div>

</div>