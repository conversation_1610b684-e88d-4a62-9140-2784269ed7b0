<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry">
    <div class="page-content">

        <?php
       // echo $scopingData['text_data']['local_seo_text_3'];
        ?>

        <h3>Lokale SEO Ranking Positionen</h3>
        <div id="local-seo-map" style="width: 100%; height: 400px; border: 1px solid #ccc; margin: 20px 0;"></div>

        <style>
            /* Print-friendly styles for the map */
            @media print {
                #local-seo-map {
                    height: 300px !important;
                    page-break-inside: avoid;
                }

                .ranking-legend {
                    page-break-inside: avoid;
                }

                /* Ensure map content is visible in print */
                #local-seo-map * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
            }
        </style>

        <div class="ranking-legend" style="margin: 10px 0;">
            <h4>Legende:</h4>
            <p style="margin: 5px 0; font-size: 14px; color: #666;">Die Zahlen auf den Markern zeigen die jeweilige Ranking-Position an.</p>
            <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #4CAF50; border: 2px solid #000; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px;">1</div>
                    <span>Position 1-2 (Sehr gut)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #C0CA33; border: 2px solid #000; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px;">3</div>
                    <span>Position 3-4 (Gut)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #FFEB3B; border: 2px solid #000; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px;">5</div>
                    <span>Position 5-6 (Mittel)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #FFA726; border: 2px solid #000; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px;">7</div>
                    <span>Position 7-8 (Schlecht)</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="width: 20px; height: 20px; background-color: #F44336; border: 2px solid #000; border-radius: 50%; margin-right: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px;">9</div>
                    <span>Position 9-10 (Sehr schlecht)</span>
                </div>
            </div>
        </div>

        <p>Die Karte zeigt die Local SEO Ranking-Positionen an verschiedenen geografischen Punkten rund um den Unternehmensstandort.</p>
    </div>
    <div class="page-footer"><?php echo $pageIndexData ?></div>
</div>

<script>
// Initialize Google Map for Local SEO analysis
function initLocalSeoMap() {
    // Default data - will be overridden with real data if available
    const mapData = {
        center: { lat: 50.1109, lng: 8.6821 }, // Frankfurt am Main as default
        points: [
            { lat: 50.1109, lng: 8.6821, position: 3, label: "Zentrum" },
            { lat: 50.1209, lng: 8.6921, position: 1, label: "Nord" },
            { lat: 50.1009, lng: 8.6721, position: 7, label: "Süd" },
            { lat: 50.1109, lng: 8.6921, position: 5, label: "Ost" },
            { lat: 50.1109, lng: 8.6721, position: 9, label: "West" }
        ]
    };

    // Try to get real data from ranking positions if available
    <?php if (!empty($rankingPositions) && is_array($rankingPositions)): ?>
        <?php
        // Find the first valid ranking position with coordinates
        $firstValidPosition = null;
        foreach ($rankingPositions as $position) {
            if (is_array($position) && isset($position['lat']) && isset($position['lon']) && isset($position['rank_absolute'])) {
                $firstValidPosition = $position;
                break;
            }
        }
        ?>

        <?php if ($firstValidPosition): ?>
            // Use the first ranking position to center the map
            mapData.center = {
                lat: <?php echo floatval($firstValidPosition['lat']); ?>,
                lng: <?php echo floatval($firstValidPosition['lon']); ?>
            };

            // Build points from all available ranking positions
            mapData.points = [];
            <?php
            $positionLabels = [
                'business_competitor_scoping_' => 'Geschäftsstandort',
                'top-left' => 'Nordwest',
                'top-right' => 'Nordost',
                'bottom-left' => 'Südwest',
                'bottom-right' => 'Südost'
            ];

            $pointIndex = 0;
            foreach ($rankingPositions as $key => $position):
                if (is_array($position) && isset($position['lat']) && isset($position['lon']) && isset($position['rank_absolute'])):
                    // Determine label based on position type
                    $label = 'Position ' . ($pointIndex + 1);
                    foreach ($positionLabels as $labelKey => $labelValue) {
                        if (is_string($key) && strpos($key, $labelKey) !== false) {
                            $label = $labelValue;
                            break;
                        } elseif (is_numeric($key) && $pointIndex < count($positionLabels)) {
                            $label = array_values($positionLabels)[$pointIndex];
                            break;
                        }
                    }

                    $rankValue = is_numeric($position['rank_absolute']) ? intval($position['rank_absolute']) : 99;
            ?>
                mapData.points.push({
                    lat: <?php echo floatval($position['lat']); ?>,
                    lng: <?php echo floatval($position['lon']); ?>,
                    position: <?php echo $rankValue; ?>,
                    label: "<?php echo addslashes($label); ?>"
                });
            <?php
                    $pointIndex++;
                endif;
            endforeach;
            ?>
        <?php endif; ?>
    <?php endif; ?>

    // Fallback: Try to get data from project info if ranking positions are not available
    <?php if (empty($rankingPositions) && !empty($projectData['info_data']['latitude']) && !empty($projectData['info_data']['longitude'])): ?>
        mapData.center = {
            lat: <?php echo floatval($projectData['info_data']['latitude']); ?>,
            lng: <?php echo floatval($projectData['info_data']['longitude']); ?>
        };

        // Generate sample points around the business location
        const centerLat = <?php echo floatval($projectData['info_data']['latitude']); ?>;
        const centerLng = <?php echo floatval($projectData['info_data']['longitude']); ?>;
        const offset = 0.01; // Approximately 1km

        mapData.points = [
            { lat: centerLat, lng: centerLng, position: 3, label: "Geschäftsstandort" },
            { lat: centerLat + offset, lng: centerLng, position: 1, label: "Nord (1km)" },
            { lat: centerLat - offset, lng: centerLng, position: 7, label: "Süd (1km)" },
            { lat: centerLat, lng: centerLng + offset, position: 5, label: "Ost (1km)" },
            { lat: centerLat, lng: centerLng - offset, position: 9, label: "West (1km)" }
        ];
    <?php endif; ?>

    // Function to get color based on ranking position
    function getMarkerColor(position) {
        if (position <= 2) return '#4CAF50';  // Grünes Mittelgrün
        if (position <= 4) return '#C0CA33';  // Olivgrün/Gelbgrün
        if (position <= 6) return '#FFEB3B';  // Warmes Gelb
        if (position <= 8) return '#FFA726';  // Bernstein / Orange
        return '#F44336';                     // Weiches Rot
    }

    // Function to get contrasting text color
    function getTextColor(backgroundColor) {
        // Convert hex to RGB
        const hex = backgroundColor.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // Return black for light colors, white for dark colors
        return luminance > 0.5 ? '#000000' : '#ffffff';
    }

    // Function to create numbered marker icon
    function createNumberedMarkerIcon(number, color) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        const size = 40;

        canvas.width = size;
        canvas.height = size;

        // Draw circle
        context.beginPath();
        context.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
        context.fillStyle = color;
        context.fill();
        context.strokeStyle = '#000000';
        context.lineWidth = 3;
        context.stroke();

        // Draw number with better contrast
        const textColor = getTextColor(color);

        // Add text shadow for better readability
        context.shadowColor = textColor === '#ffffff' ? '#000000' : '#ffffff';
        context.shadowBlur = 2;
        context.shadowOffsetX = 1;
        context.shadowOffsetY = 1;

        context.fillStyle = textColor;
        context.font = 'bold 16px Arial';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(number.toString(), size/2, size/2);

        // Reset shadow
        context.shadowColor = 'transparent';
        context.shadowBlur = 0;
        context.shadowOffsetX = 0;
        context.shadowOffsetY = 0;

        return canvas.toDataURL();
    }

    // Create map with all controls disabled
    const map = new google.maps.Map(document.getElementById('local-seo-map'), {
        zoom: 14,
        center: mapData.center,
        mapTypeId: 'roadmap',
        disableDefaultUI: true,  // Disable all default UI controls
        zoomControl: false,      // Disable zoom buttons
        mapTypeControl: false,   // Disable map type selector
        scaleControl: false,     // Disable scale control
        streetViewControl: false, // Disable street view pegman
        rotateControl: false,    // Disable rotate control
        fullscreenControl: false, // Disable fullscreen button
        gestureHandling: 'none'  // Disable all mouse/touch interactions
    });

    // Add markers for each point
    mapData.points.forEach((point, index) => {
        // Create custom marker with number
        const marker = new google.maps.Marker({
            position: { lat: point.lat, lng: point.lng },
            map: map,
            title: `${point.label} - Position ${point.position}`,
            icon: {
                url: createNumberedMarkerIcon(point.position, getMarkerColor(point.position)),
                scaledSize: new google.maps.Size(40, 40),
                anchor: new google.maps.Point(20, 20)
            }
        });

        // Add info window
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h4>${point.label}</h4>
                    <p><strong>Ranking Position:</strong> ${point.position}</p>
                    <p><strong>Koordinaten:</strong> ${point.lat.toFixed(4)}, ${point.lng.toFixed(4)}</p>
                </div>
            `
        });

        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    });

    // Add a circle to show the general area
    const circle = new google.maps.Circle({
        strokeColor: '#0066cc',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#0066cc',
        fillOpacity: 0.1,
        map: map,
        center: mapData.center,
        radius: 2000 // 2km radius
    });
}

// Load Google Maps API if not already loaded
function loadGoogleMapsAPI() {
    if (typeof google !== 'undefined' && google.maps) {
        initLocalSeoMap();
        return;
    }

    // Check if script is already being loaded
    if (document.querySelector('script[src*="maps.googleapis.com"]')) {
        // Wait for it to load
        const checkGoogleMaps = setInterval(() => {
            if (typeof google !== 'undefined' && google.maps) {
                clearInterval(checkGoogleMaps);
                initLocalSeoMap();
            }
        }, 100);
        return;
    }

    // Load Google Maps API
    const script = document.createElement('script');

    // Try to get API key from configuration
    let apiKey = '';
    <?php
    // Try to get Google Maps API key from various sources
    $googleMapsApiKey = '';

    // Check if there's a config file or environment variable
    $googleMapsApiKey = 'AIzaSyA9gqsEneUPZFBkG_JtMb_pskmtP4KeUDM';

    echo "apiKey = " . json_encode($googleMapsApiKey) . ";";
    ?>

    if (apiKey) {
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initLocalSeoMap`;
    } else {
        script.src = 'https://maps.googleapis.com/maps/api/js?callback=initLocalSeoMap';
    }

    script.async = true;
    script.defer = true;

    // Fallback if no API key is available
    script.onerror = function() {
        console.warn('Google Maps API could not be loaded. Please add a valid API key.');
        showStaticMapFallback();
    };

    // Show static map fallback
    function showStaticMapFallback() {
        const mapContainer = document.getElementById('local-seo-map');
        mapContainer.innerHTML = `
            <div style="position: relative; height: 400px; background: linear-gradient(45deg, #e8f4f8 25%, transparent 25%), linear-gradient(-45deg, #e8f4f8 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e8f4f8 75%), linear-gradient(-45deg, transparent 75%, #e8f4f8 75%); background-size: 20px 20px; background-position: 0 0, 0 10px, 10px -10px, -10px 0px; border: 1px solid #ddd;">
                <div style="position: absolute; top: 10px; left: 10px; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                    <h4 style="margin: 0 0 10px 0; color: #333;">Local SEO Ranking Positionen</h4>
                    <div style="font-size: 12px; color: #666;">
                        <div style="margin: 5px 0; display: flex; align-items: center;">📍 Zentrum: Position <span style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; background: #ffff00; border: 1px solid #000; border-radius: 50%; margin: 0 5px; font-weight: bold; font-size: 10px; color: #000;">3</span></div>
                        <div style="margin: 5px 0; display: flex; align-items: center;">📍 Nord: Position <span style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; background: #00ff00; border: 1px solid #000; border-radius: 50%; margin: 0 5px; font-weight: bold; font-size: 10px; color: #000;">1</span></div>
                        <div style="margin: 5px 0; display: flex; align-items: center;">📍 Süd: Position <span style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; background: #ff8000; border: 1px solid #000; border-radius: 50%; margin: 0 5px; font-weight: bold; font-size: 10px; color: #000;">7</span></div>
                        <div style="margin: 5px 0; display: flex; align-items: center;">📍 Ost: Position <span style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; background: #ffff00; border: 1px solid #000; border-radius: 50%; margin: 0 5px; font-weight: bold; font-size: 10px; color: #000;">5</span></div>
                        <div style="margin: 5px 0; display: flex; align-items: center;">📍 West: Position <span style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; background: #ff0000; border: 1px solid #000; border-radius: 50%; margin: 0 5px; font-weight: bold; font-size: 10px; color: #fff;">9</span></div>
                    </div>
                </div>

                <!-- Simulated map points with numbers -->
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: #ffff00; border: 3px solid #000; border-radius: 50%; z-index: 10; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; color: #000;" title="Zentrum - Position 3">3</div>
                <div style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: #00ff00; border: 3px solid #000; border-radius: 50%; z-index: 10; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; color: #000;" title="Nord - Position 1">1</div>
                <div style="position: absolute; top: 70%; left: 50%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: #ff8000; border: 3px solid #000; border-radius: 50%; z-index: 10; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; color: #000;" title="Süd - Position 7">7</div>
                <div style="position: absolute; top: 50%; left: 70%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: #ffff00; border: 3px solid #000; border-radius: 50%; z-index: 10; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; color: #000;" title="Ost - Position 5">5</div>
                <div style="position: absolute; top: 50%; left: 30%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: #ff0000; border: 3px solid #000; border-radius: 50%; z-index: 10; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; color: #000;" title="West - Position 9">9</div>

                <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 5px 10px; border-radius: 3px; font-size: 11px; color: #666;">
                    Statische Kartenansicht<br>
                    <small>Für interaktive Karte Google Maps API konfigurieren</small>
                </div>
            </div>
        `;
    }

    document.head.appendChild(script);
}

// Initialize map when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadGoogleMapsAPI);
} else {
    loadGoogleMapsAPI();
}
</script>
