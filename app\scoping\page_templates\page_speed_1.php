<?php
/**
 * PageSpeed Performance Comparison Template
 *
 * This template displays PageSpeed performance data comparing the main project
 * with its competitors in a tabular format.
 *
 * Expected data structure: $pageSpeedData array with project and competitor data
 * Template variables available:
 * $scopingData - Contains the scoping data from the database
 * $projectData - Contains the related project data (if available)
 * $pageSpeedData - Contains PageSpeed performance data for comparison
 */

if (empty($pageSpeedData)) {
    echo '<div class="alert alert-warning">Keine PageSpeed-Daten verfügbar.</div>';
    return;
}

// Helper function to get performance class based on score
function getPerformanceClass($score) {
    if ($score >= 90) return 'text-success fw-bold';
    if ($score >= 50) return 'text-warning fw-bold';
    return 'text-danger fw-bold';
}

// Helper function to get metric class based on score
function getMetricClass($score) {
    if ($score >= 90) return 'bg-success text-white';
    if ($score >= 50) return 'bg-warning text-dark';
    return 'bg-danger text-white';
}

// Define key metrics for display
$keyMetrics = [
    'first-contentful-paint' => 'First Contentful Paint',
    'largest-contentful-paint' => 'Largest Contentful Paint',
    'total-blocking-time' => 'Total Blocking Time',
    'cumulative-layout-shift' => 'Cumulative Layout Shift',
    'speed-index' => 'Speed Index',
    'interactive' => 'Time to Interactive'
];
?>

<div class="page-entry">
    <div class="page-content">
        <h1>Pagespeed Mobil + Desktop</h1>

        <?php if (!empty($scopingData['text_data']['page_speed_text_1'])): ?>
            <div class="mb-4">
                <?php echo $scopingData['text_data']['page_speed_text_1']; ?>
            </div>
        <?php endif; ?>

        <div class="page-speed-comparison">
            <!-- Overall Performance Scores -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Performance Scores Übersicht</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Website</th>
                                    <th class="text-center">Mobile Performance</th>
                                    <th class="text-center">Desktop Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pageSpeedData as $index => $data): ?>
                                    <tr <?php echo $index === 0 ? 'class="table-primary"' : ''; ?>>
                                        <td>
                                            <strong><?php echo htmlspecialchars($data['name']); ?></strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge <?php echo getPerformanceClass($data['mobile_performance']); ?> fs-3">
                                                <?php echo $data['mobile_performance']; ?>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge <?php echo getPerformanceClass($data['desktop_performance']); ?> fs-6">
                                                <?php echo $data['desktop_performance']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="page-footer"><?php echo $pageIndexData ?></div>
</div>

<style>
.page-speed-comparison .badge {
    font-size: 0.9em;
}

.page-speed-comparison .table th {
    border-top: none;
}

.page-speed-comparison .alert {
    margin-bottom: 0.5rem;
}

.page-speed-comparison .card-header h5 {
    color: #495057;
}
</style>
