<?php
/**
 * PageSpeed Performance Comparison Template
 *
 * This template displays PageSpeed performance data comparing the main project
 * with its competitors in a tabular format.
 *
 * Expected data structure: $pageSpeedData array with project and competitor data
 * Template variables available:
 * $scopingData - Contains the scoping data from the database
 * $projectData - Contains the related project data (if available)
 * $pageSpeedData - Contains PageSpeed performance data for comparison
 */

if (empty($pageSpeedData)) {
    echo '<div class="alert alert-warning">Keine PageSpeed-Daten verfügbar.</div>';
    return;
}

// Define key metrics for display
$keyMetrics = [
    'first-contentful-paint' => 'First Contentful Paint',
    'largest-contentful-paint' => 'Largest Contentful Paint',
    'total-blocking-time' => 'Total Blocking Time',
    'cumulative-layout-shift' => 'Cumulative Layout Shift',
    'speed-index' => 'Speed Index',
    'interactive' => 'Time to Interactive'
];
?>

<div class="page-entry">
    <div class="page-content">
        <h3>Mobile Performance Details</h3>

        <?php if (!empty($scopingData['text_data']['page_speed_text_2'])): ?>
            <div class="mb-4">
                <?php echo $scopingData['text_data']['page_speed_text_2']; ?>
            </div>
        <?php endif; ?>

        <div class="page-speed-comparison">
            <!-- Overall Performance Scores -->

            <!-- Detailed Mobile Metrics Comparison -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Mobile Performance Metriken im Detail</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-dark">
                                <tr>
                                    <th>Metrik</th>
                                    <?php foreach ($pageSpeedData as $index => $data): ?>
                                        <th class="text-center">
                                            <?php
                                            $shortName = explode(' | ', $data['name'])[0];

                                            if ($index !== 0) {
                                                $shortName = 'W'.$index;
                                            }

                                            echo htmlspecialchars($shortName);
                                            ?>
                                            <?php if ($index === 0): ?>
                                                <br><small class="text-warning">(Ihr Projekt)</small>
                                            <?php endif; ?>
                                        </th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($keyMetrics as $metricKey => $metricName): ?>
                                    <tr>
                                        <td class="fw-bold"><?php echo $metricName; ?></td>
                                        <?php foreach ($pageSpeedData as $index => $data): ?>
                                            <td class="text-center">
                                                <?php if (isset($data['key_metrics_mobile'][$metricKey])): ?>
                                                    <?php $metric = $data['key_metrics_mobile'][$metricKey]; ?>
                                                    <div class="mb-1">
                                                        <span class="badge <?php echo getMetricClass($metric['score']); ?>">
                                                            <?php echo $metric['score']; ?>
                                                        </span>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars($metric['display_value']); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Performance Analysis Summary -->
            <div class="card d-none">
                <div class="card-header">
                    <h5 class="mb-0">Analyse Zusammenfassung</h5>
                </div>
                <div class="card-body">
                    <?php
                    // Calculate position and performance analysis
                    $mainProject = $pageSpeedData[0];
                    $competitors = array_slice($pageSpeedData, 1);

                    // Mobile performance ranking
                    $mobileRanking = 1;
                    foreach ($competitors as $competitor) {
                        if ($competitor['mobile_performance'] > $mainProject['mobile_performance']) {
                            $mobileRanking++;
                        }
                    }

                    // Desktop performance ranking
                    $desktopRanking = 1;
                    foreach ($competitors as $competitor) {
                        if ($competitor['desktop_performance'] > $mainProject['desktop_performance']) {
                            $desktopRanking++;
                        }
                    }

                    $totalProjects = count($pageSpeedData);
                    ?>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Mobile Performance Ranking</h6>
                            <p class="mb-2">
                                <strong>Position <?php echo $mobileRanking; ?> von <?php echo $totalProjects; ?></strong>
                                <span class="badge <?php echo $mobileRanking === 1 ? 'bg-success' : ($mobileRanking <= ceil($totalProjects/2) ? 'bg-warning' : 'bg-danger'); ?> ms-2">
                                    <?php echo $mainProject['mobile_performance']; ?> Punkte
                                </span>
                            </p>

                            <?php if ($mobileRanking === 1): ?>
                                <div class="alert alert-success py-2">
                                    <i class="fas fa-trophy"></i> Beste Mobile Performance!
                                </div>
                            <?php elseif ($mobileRanking > ceil($totalProjects/2)): ?>
                                <div class="alert alert-warning py-2">
                                    <i class="fas fa-exclamation-triangle"></i> Mobile Performance verbesserungswürdig
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <h6>Desktop Performance Ranking</h6>
                            <p class="mb-2">
                                <strong>Position <?php echo $desktopRanking; ?> von <?php echo $totalProjects; ?></strong>
                                <span class="badge <?php echo $desktopRanking === 1 ? 'bg-success' : ($desktopRanking <= ceil($totalProjects/2) ? 'bg-warning' : 'bg-danger'); ?> ms-2">
                                    <?php echo $mainProject['desktop_performance']; ?> Punkte
                                </span>
                            </p>

                            <?php if ($desktopRanking === 1): ?>
                                <div class="alert alert-success py-2">
                                    <i class="fas fa-trophy"></i> Beste Desktop Performance!
                                </div>
                            <?php elseif ($desktopRanking > ceil($totalProjects/2)): ?>
                                <div class="alert alert-warning py-2">
                                    <i class="fas fa-exclamation-triangle"></i> Desktop Performance verbesserungswürdig
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Key Insights -->
                    <div class="mt-3">
                        <h6>Wichtige Erkenntnisse</h6>
                        <ul class="list-unstyled">
                            <?php
                            // Find best and worst metrics
                            $bestMetrics = [];
                            $worstMetrics = [];

                            foreach ($keyMetrics as $metricKey => $metricName) {
                                if (isset($mainProject['key_metrics_mobile'][$metricKey])) {
                                    $score = $mainProject['key_metrics_mobile'][$metricKey]['score'];
                                    if ($score >= 90) {
                                        $bestMetrics[] = $metricName;
                                    } elseif ($score < 50) {
                                        $worstMetrics[] = $metricName;
                                    }
                                }
                            }
                            ?>

                            <?php if (!empty($bestMetrics)): ?>
                                <li class="text-success mb-1">
                                    <i class="fas fa-check-circle"></i>
                                    <strong>Stärken:</strong> <?php echo implode(', ', $bestMetrics); ?>
                                </li>
                            <?php endif; ?>

                            <?php if (!empty($worstMetrics)): ?>
                                <li class="text-danger mb-1">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <strong>Verbesserungspotential:</strong> <?php echo implode(', ', $worstMetrics); ?>
                                </li>
                            <?php endif; ?>

                            <li class="text-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Gesamt-Bewertung:</strong>
                                <?php
                                $avgScore = ($mainProject['mobile_performance'] + $mainProject['desktop_performance']) / 2;
                                if ($avgScore >= 85) {
                                    echo "Sehr gute Performance";
                                } elseif ($avgScore >= 70) {
                                    echo "Gute Performance mit Optimierungspotential";
                                } else {
                                    echo "Performance sollte dringend optimiert werden";
                                }
                                ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="page-footer"><?php echo $pageIndexData ?></div>
</div>

<style>
.page-speed-comparison .badge {
    font-size: 0.9em;
}

.page-speed-comparison .table th {
    border-top: none;
}

.page-speed-comparison .alert {
    margin-bottom: 0.5rem;
}

.page-speed-comparison .card-header h5 {
    color: #495057;
}
</style>
