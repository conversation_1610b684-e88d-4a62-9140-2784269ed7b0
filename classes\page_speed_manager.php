<?php

/**
 * PageSpeedManager Class
 *
 * This class manages PageSpeed Insights analysis for projects.
 * It handles adding entries to the page_speed_insights table and processing unfinished entries.
 */
class PageSpeedManager
{
    /** @var DbManager */
    private $dbManager;

    /** @var string */
    private $apiKey;

    /** @var int */
    private $timeout = 60;

    /** @var array */
    private $categories = [
        PageSpeedInsights::CATEGORY_PERFORMANCE,
        PageSpeedInsights::CATEGORY_ACCESSIBILITY,
        PageSpeedInsights::CATEGORY_BEST_PRACTICES,
        PageSpeedInsights::CATEGORY_SEO
    ];

    /**
     * Constructor
     *
     * @param DbManager $dbManager The database manager instance
     */
    public function __construct()
    {
        global $dbManager;
        $this->dbManager = $dbManager;

        // Get API key from configuration
        $this->apiKey = ConfigManager::get('google.page_insights_api_key');

        if (empty($this->apiKey)) {
            throw new InvalidArgumentException("Google PageSpeed API key not found in configuration.");
        }
    }

    /**
     * Add a new entry in the page_speed_insights table for a project
     *
     * @param int $projectId The project ID
     * @return int|false The ID of the new entry or false on failure
     */
    public function addEntry($projectId)
    {
        try {
            // Check if there's already an unfinished entry for this project
            $existingEntries = $this->dbManager->findByValues(
                [
                    'project_id' => $projectId,
                    'is_finished' => 0
                ],
                'page_speed_insights'
            );

            // If there's already an unfinished entry, return false
            if (!empty($existingEntries)) {
                return false;
            }

            // Get the project to find the website_url
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project) || empty($project['website_url'])) {
                return false;
            }

            // Create a new entry
            $data = [
                'project_id' => $projectId,
                'is_finished' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Insert the new entry
            $newId = $this->dbManager->insertNewRowByData('page_speed_insights', $data);

            return $newId;
        } catch (Exception $e) {
            // Log error if needed
            return false;
        }
    }

    /**
     * Handle one unfinished entry in the page_speed_insights table
     * Processes only the oldest unfinished entry
     *
     * @return array Statistics about the processed entry
     */
    public function handleEntries(): array
    {
        $stats = [
            'entries_processed' => 0,
            'entries_succeeded' => 0,
            'entries_failed' => 0
        ];

        try {
            // Find the oldest unfinished entry
            $entries = $this->dbManager->findByValues(
                ['is_finished' => 0],
                'page_speed_insights',
                'created_at ASC',
                1 // Limit to 1 entry
            );

            if (empty($entries)) {
                return $stats;
            }

            // Process only the first (oldest) entry
            $entry = $entries[0];
            $stats['entries_processed'] = 1;

            $result = $this->processEntry($entry);

            if ($result) {
                $stats['entries_succeeded'] = 1;
            } else {
                $stats['entries_failed'] = 1;
            }

            return $stats;
        } catch (Exception $e) {
            // Log error if needed
            return $stats;
        }
    }

    /**
     * Process a single page_speed_insights entry
     *
     * @param array $entry The entry to process
     * @return bool True on success, false on failure
     */
    private function processEntry(array $entry): bool
    {
        try {
            $projectId = $entry['project_id'];
            $entryId = $entry['id'];

            // Get the project to find the website_url
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project) || empty($project['website_url'])) {
                return false;
            }

            $websiteUrl = $project['website_url'];

            // Create PageSpeedInsights instance
            $psi = new PageSpeedInsights($this->apiKey, $this->timeout);

            // Run analysis for mobile
            $mobileResults = $this->runAnalysis($psi, $websiteUrl, PageSpeedInsights::STRATEGY_MOBILE);

            // Run analysis for desktop
            $desktopResults = $this->runAnalysis($psi, $websiteUrl, PageSpeedInsights::STRATEGY_DESKTOP);

            // Add timestamp to each result set
            $mobileResults['analyzed_at'] = date('Y-m-d H:i:s');
            $desktopResults['analyzed_at'] = date('Y-m-d H:i:s');

            // Update the entry with separate fields for mobile and desktop
            $updateData = [
                'mobile_insights_data' => json_encode($mobileResults),
                'desktop_insights_data' => json_encode($desktopResults),
                'is_finished' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $success = $this->dbManager->updateFields($entryId, 'page_speed_insights', array_keys($updateData), $updateData);

            return $success;
        } catch (Exception $e) {
            // Log error if needed
            return false;
        }
    }

    /**
     * Get the last finished PageSpeed entry for a project
     *
     * @param int $projectId The project ID
     * @return array|null The entry data or null if not found
     */
    public function getLastEntry(int $projectId): ?array
    {
        try {
            // Find the latest finished entry for the project
            $entries = $this->dbManager->findByValues(
                [
                    'project_id' => $projectId,
                    'is_finished' => 1
                ],
                'page_speed_insights',
                'updated_at DESC',
                1 // Limit to 1 entry
            );

            if (empty($entries)) {
                return null;
            }

            $entry = $entries[0];

            return $entry;
        } catch (Exception $e) {
            // Log error if needed
            return null;
        }
    }

    /**
     * Get the last mobile performance score for a project
     *
     * @param int $projectId The project ID
     * @return int|null The mobile performance score (0-100) or null if not found
     */
    public function getLastMobileScore(int $projectId): ?int
    {
        try {
            $entry = $this->getLastEntry($projectId);

            if (empty($entry) || empty($entry['mobile_insights_data'])) {
                return null;
            }

            $mobileData = $entry['mobile_insights_data'];

            if (!$mobileData || !isset($mobileData['scores']['performance'])) {
                return null;
            }

            return (int) $mobileData['scores']['performance'];
        } catch (Exception $e) {
            // Log error if needed
            return null;
        }
    }

    /**
     * Get the last desktop performance score for a project
     *
     * @param int $projectId The project ID
     * @return int|null The desktop performance score (0-100) or null if not found
     */
    public function getLastDesktopScore(int $projectId): ?int
    {
        try {
            $entry = $this->getLastEntry($projectId);

            if (empty($entry) || empty($entry['desktop_insights_data'])) {
                return null;
            }

            $desktopData = $entry['desktop_insights_data'];

            if (!$desktopData || !isset($desktopData['scores']['performance'])) {
                return null;
            }

            return (int) $desktopData['scores']['performance'];
        } catch (Exception $e) {
            // Log error if needed
            return null;
        }
    }

    /**
     * Get the last key metric score for a project
     *
     * @param int $projectId The project ID
     * @param string $keyMetric The key metric name (e.g., 'first-contentful-paint', 'largest-contentful-paint', etc.)
     * @param string $device The device type ('mobile' or 'desktop')
     * @return int|null The metric score (0-100) or null if not found
     */
    public function getLastKeyMetricScore(int $projectId, string $keyMetric, string $device): ?int
    {
        try {
            $entry = $this->getLastEntry($projectId);

            if (empty($entry)) {
                return null;
            }

            // Determine which data field to use based on device
            $dataField = $device === 'mobile' ? 'mobile_insights_data' : 'desktop_insights_data';

            if (empty($entry[$dataField])) {
                return null;
            }

            $data = $entry[$dataField];

            if (!$data || !isset($data['key_metrics'][$keyMetric]['score'])) {
                return null;
            }

            return (int) $data['key_metrics'][$keyMetric]['score'];
        } catch (Exception $e) {
            // Log error if needed
            return null;
        }
    }

    /**
     * Run PageSpeed Insights analysis for a specific strategy
     *
     * @param PageSpeedInsights $psi The PageSpeedInsights instance
     * @param string $url The URL to analyze
     * @param string $strategy The strategy to use (mobile or desktop)
     * @return array The analysis results with only important metrics
     */
    private function runAnalysis(PageSpeedInsights $psi, string $url, string $strategy): array
    {
        $results = [
            'success' => false,
            'scores' => [],
            'key_metrics' => [],
            'top_opportunities' => [],
            'error' => null
        ];

        // Run the analysis
        if ($psi->analyze($url, $strategy, $this->categories)) {
            $results['success'] = true;

            // Get all scores (performance, accessibility, best-practices, seo)
            $allScores = $psi->getAllScores();
            foreach ($allScores as $category => $score) {
                $results['scores'][$category] = round($score * 100); // Convert to percentage
            }

            // Get only the key metrics with their display values
            $metrics = $psi->getMetrics();
            $keyMetricIds = [
                'first-contentful-paint',
                'largest-contentful-paint',
                'total-blocking-time',
                'cumulative-layout-shift',
                'speed-index',
                'interactive' // Time to Interactive
            ];

            foreach ($keyMetricIds as $metricId) {
                if (!empty($metrics[$metricId])) {
                    $results['key_metrics'][$metricId] = [
                        'display_value' => $metrics[$metricId]->displayValue,
                        'score' => isset($metrics[$metricId]->score) ? round($metrics[$metricId]->score * 100) : null,
                        'numeric_value' => $metrics[$metricId]->numericValue ?? null
                    ];
                }
            }

            // Get top 5 opportunities with potential savings
            $opportunities = $psi->getOpportunities();
            $count = 0;
            foreach ($opportunities as $id => $opportunity) {
                $results['top_opportunities'][] = [
                    'title' => $opportunity->title ?? $id,
                    'savings_ms' => $opportunity->details->overallSavingsMs ?? null,
                    'description' => $opportunity->description ?? null
                ];

                if (++$count >= 5) break; // Only include top 5 opportunities
            }
        } else {
            $results['error'] = $psi->getLastError();
        }

        return $results;
    }
}
