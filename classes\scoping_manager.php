<?php

/**
 * ScopingManager Class
 *
 * This class manages project scopings, handling the creation and management
 * of scoping entries in the scopings table.
 */
class ScopingManager
{
    /** @var DbManager */
    private $dbManager;

    /** @var DataForSeoManager */
    private $dfsManager;

    /** @var DataForSeoManager */
    private $projectManager;

    /** @var AiManager */
    private $aiManager;

    /**
     * Constructor
     *
     * @param DbManager $dbManager The database manager instance
     */
    public function __construct()
    {
        global $dbManager;
        $this->dbManager = $dbManager;

        $this->dfsManager = new DataForSeoManager( );
        $this->projectManager = new ProjectManager( );
        $this->aiManager = new AiManager( );
    }

    /**
     * Add a scoping entry by website URL
     *
     * This function finds a project with the given website URL or creates a new one,
     * then adds a scoping entry for that project.
     *
     * @param string $websiteUrl The website URL
     * @param array $options Optional options data to store (will be JSO<PERSON> encoded)
     * @param string $text Optional text data to store
     * @return array Response array with success status and scoping data
     */
    public function addScopingByWebsiteUrl(string $websiteUrl, array $options = [], string $text = ''): array
    {
        $response = ['success' => 0];

        // Validate website URL
        if (empty($websiteUrl)) {
            $response['error'] = 'Bitte geben Sie eine Website-URL an.';
            return $response;
        }

        // Ensure URL has a scheme
        $websiteUrl = WebsiteManager::ensureUrlHasScheme($websiteUrl);

        try {
            // Check if a project with this website URL already exists
            $existingProject = $this->dbManager->findByColumn('website_url', $websiteUrl, 'projects');

            // If project exists, use its ID
            if (!empty($existingProject)) {
                $projectId = $existingProject[0]['id'];
                $response['project_exists'] = true;
                $response['project_id'] = $projectId;
            } else {
                // Project doesn't exist, create a new one
                $projectManager = new ProjectManager();
                $projectResult = $projectManager->addProject($websiteUrl);
                $projectManager->generateInfoData( $projectResult['last_id'] );

                if (!$projectResult['success']) {
                    $response['error'] = 'Fehler beim Erstellen des Projekts: ' . ($projectResult['error'] ?? 'Unbekannter Fehler');
                    return $response;
                }

                $projectId = $projectResult['last_id'];
                $response['project_created'] = true;
                $response['project_id'] = $projectId;
                $response['project_data'] = $projectResult['project_data'];
            }

            // Now add a scoping entry for this project
            $scopingResult = $this->addScopingByProjectId($projectId, $options, $text);

            if (!$scopingResult['success']) {
                $response['error'] = 'Fehler beim Erstellen des Scopings: ' . ($scopingResult['error'] ?? 'Unbekannter Fehler');
                return $response;
            }

            // Merge the scoping result with our response
            $response['success'] = 1;
            $response['last_id'] = $scopingResult['last_id'];
            $response['message'] = 'Scoping erfolgreich angelegt' .
                ($response['project_created'] ? ' mit neuem Projekt.' : ' für bestehendes Projekt.');
            $response['scoping_data'] = $scopingResult['scoping_data'];

        } catch (Exception $e) {
            $response['error'] = 'Fehler: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Add a new scoping entry for a project
     *
     * @param int $projectId The project ID
     * @param array $options Optional options data to store (will be JSON encoded)
     * @param string $text Optional text data to store
     * @return array Response array with success status and scoping data
     */
    public function addScopingByProjectId($projectId, array $options = [], string $text = ''): array
    {
        $response = ['success' => 0];

        // Validate project ID
        if (empty($projectId) || !is_numeric($projectId)) {
            $response['error'] = 'Ungültige Projekt-ID.';
            return $response;
        }

        try {
            // Check if the project exists
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $response['error'] = 'Das angegebene Projekt existiert nicht.';
                return $response;
            }

            // Prepare data for insertion
            $data = [
                'project_id' => $projectId,
                'is_completed' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Add options_data if provided
            if (!empty($options)) {
                $data['options_data'] = json_encode($options);
            }

            // Add text_data if provided
            if (!empty($text)) {
                $data['text_data'] = $text;
            }

            // Insert the new scoping entry
            $lastId = $this->dbManager->insertNewRowByData('scopings', $data);

            if ($lastId) {
                $response['success'] = 1;
                $response['last_id'] = $lastId;
                $response['message'] = 'Scoping erfolgreich angelegt.';
                $response['scoping_data'] = $data;
            } else {
                $response['error'] = 'Fehler beim Anlegen des Scopings.';
            }
        } catch (Exception $e) {
            $response['error'] = 'Datenbankfehler: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Process pending scoping entries
     *
     * Fetches all scoping entries with is_completed = 0 (pending)
     *
     * @param int|null $limit Optional limit for the number of entries to fetch
     * @return array Array of pending scoping entries
     */
    public function processPending($limit = null): array
    {
        try {
            // Find all pending scoping entries
            $pendingScopings = $this->dbManager->findByValues(
                ['is_completed' => 0],
                'scopings',
                'created_at ASC',
                $limit
            );

            if (empty($pendingScopings)) {
                return [];
            }

            foreach ( $pendingScopings as $pendingScoping ) {
                $this->processScopingByScopingId( $pendingScoping['id'] );
            }

            return $pendingScopings;
        } catch (Exception $e) {
            // Log error if needed
            error_log("Error in ScopingManager::processPending: " . $e->getMessage());
            return [];
        }
    }

    public function findScopingById($scopingId)
    {
        return $this->dbManager->findById( $scopingId, 'scopings' );
    }

    /**
     * Get rendered scoping pages from templates
     *
     * Renders PHP templates in app/scoping/page_templates/ directory
     * and returns the HTML as an array
     *
     * @param int $scopingId The scoping ID to pass to templates
     * @param int|null $pageIndex Optional specific page index to render. If null, renders all pages.
     * @return array Array of rendered HTML strings
     */
    public function getRenderedScopingPages(int $scopingId): array
    {
        $renderedPages = [];
        $templatesPath = APP_PATH . 'scoping/page_templates/';

        try {
            // Validate scoping ID
            if (empty($scopingId) || !is_numeric($scopingId)) {
                error_log("ScopingManager::getRenderedScopingPages: Invalid scoping ID: " . $scopingId);
                return $renderedPages;
            }

            // Get scoping data from database
            $scoping = $this->findScopingById($scopingId);
            if (empty($scoping)) {
                error_log("ScopingManager::getRenderedScopingPages: Scoping not found for ID: " . $scopingId);
                return $renderedPages;
            }

            // Get related project data
            $project = null;
            if (!empty($scoping['project_id'])) {
                $project = $this->projectManager->findProjectById($scoping['project_id']);
            }

            // Check if the templates directory exists
            if (!is_dir($templatesPath)) {
                error_log("ScopingManager::getRenderedScopingPages: Templates directory not found: " . $templatesPath);
                return $renderedPages;
            }

            // Fixed array of files to determine pages getting rendered
            $files = [
                'front.php',
                'local_seo_1.php',
                'local_seo_map.php',
                'local_seo_3.php',
                'seo_1.php',
                'page_speed_1.php',
                'page_speed_2.php',
            ];

            // Build template files array based on fixed files array
            $templateFiles = [];
            foreach ($files as $fileName) {
                $filePath = $templatesPath . $fileName;
                if (file_exists($filePath)) {
                    $templateFiles[] = $filePath;
                } else {
                    error_log("ScopingManager::getRenderedScopingPages: Template file not found: " . $filePath);
                }
            }

            if (empty($templateFiles)) {
                error_log("ScopingManager::getRenderedScopingPages: No valid template files found from fixed files array");
                return $renderedPages;
            }

            // Render each template
            $pageIndex = 0;
            foreach ($templateFiles as $currentTemplateIndex => $templateFile) {
                try {
                    // Start output buffering
                    ob_start();

                    // Make scoping and project data available to templates
                    $scopingData = $scoping;
                    $projectData = $project;

                    $pageIndexData = $pageIndex;

                    $positions = $this->getPositions();

                    $pageSpeedData = $this->getPageSpeedData($scoping['id']);

                    $rankingPositions = [];
                    $rankingPositions[] = $this->getRankingPositionDetailsByTag(  $scopingId, 'business_competitor_scoping_' . $scopingId );
                    foreach ( $positions as $position ){
                        $rankingPositions[] = $this->getRankingPositionDetailsByTag(  $scopingId, 'ranking_scoping_'.$position.'_' . $scopingId );
                    }

                    // Include the template file
                    include $templateFile;

                    // Get the rendered content
                    $renderedContent = ob_get_clean();

                    // Add to the array if content was generated
                    if (!empty(trim($renderedContent))) {
                        $renderedPages[] = $renderedContent;
                    }

                } catch (Exception $e) {
                    // Clean the output buffer in case of error
                    if (ob_get_level()) {
                        ob_end_clean();
                    }
                    error_log("ScopingManager::getRenderedScopingPages: Error rendering template " . basename($templateFile) . ": " . $e->getMessage());
                }

                $pageIndex++;
            }

        } catch (Exception $e) {
            error_log("ScopingManager::getRenderedScopingPages: General error: " . $e->getMessage());
        }

        return $renderedPages;
    }

    private function processScopingByScopingId( $scopingId )
    {
        $scoping = $this->findScopingById( $scopingId );

        if( ! $scoping )
            return null;

        //DFS business data ziehen anhand info_data
        $this->handleDfsBusinessDataByScoping( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_dfs_business_data_finished' ))
            return;

        //competitors ziehen:
        $this->handleDfsCompetitorDataByScoping( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_dfs_competitor_data_finished' ))
            return;

        //competitors anlegen:
        $this->handleAddCompetitors( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_add_competitor_finished' ))
            return;

        //Local Ranking Positionen:
        $this->handleRankingPositions( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_handle_ranking_positions_finished' ))
            return;

        //Performance check:
        $this->handlePageSpeed( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_page_speed_finished' ))
            return;

        $this->handleGenerateAiText( $scoping );


    }

    //HANDLER #######################################

    private function handleGenerateAiText($scoping)
    {
        $scopingId = $scoping['id'];

        $project = $this->findProjectById($scoping['project_id']);

        if(!$project) {
            $this->setStatus( $scopingId , 'error' , 'handleGenerateAiText - Konnte project nicht finden!');
            return;
        }

        $projectData = $project['info_data'];
        unset($projectData['summary']);

        $websiteHtml = $scoping['website_html'];
        if( ! $scoping['website_html'] ) {
            $websiteManager = new WebsiteManager($this->aiManager);
            $websiteManager->analyzeWebsite( $project['website_url'] );
            $websiteHtml = $websiteManager->getHtml();

            $this->updateWebsiteHtml( $scopingId, $websiteHtml );
        }else {
            $websiteManager = new WebsiteManager($this->aiManager);
            $websiteManager->setHtml( $websiteHtml );
        }

        $websiteFullText = $websiteManager->getFullText();

        if(!$websiteFullText) {
            $this->setStatus( $scopingId , 'error' , 'handleGenerateAiText - Konnte webseite nicht abrufen!');
            return;
        }

        $this->aiManager->setDefaultProvider('openrouter');
        $this->aiManager->setModel('microsoft/mai-ds-r1:free');

        $businessData = $this->getBusinessData($scopingId);
        $positions = $this->getPositions();
        $rankingPositions = [];
        $rankingPositions['standort'] = $this->getRankingPositionByTag(  $scopingId,'business_data_scoping_' . $scopingId );

        $ratingsCompetitors = $this->getRatingsDataByTag( $scopingId,'business_competitor_scoping_' . $scopingId);

        foreach($positions as $position) {
            $rankingPositions[$position] = $this->getRankingPositionByTag(  $scopingId, 'ranking_scoping_'.$position.'_' . $scopingId );
        }

        //Local SEO Text 1, Wettbewerber, Rankings besorgen, Map ausgeben
        if( ! $this->isTextWritten( $scoping , 'local_seo_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'GOOGLE BUSINESS DATA:'.PHP_EOL;
            $prompt .= print_r($businessData, true);

            $prompt .= 'RANKING für Suchbegriff "Zahnarzt" an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            $prompt .= print_r($rankingPositions, true);

            $prompt .= 'BEWERTUNGEN VON WETTBEWERBERN:'.PHP_EOL;
            $prompt .= print_r($ratingsCompetitors, true);

            $prompt .= 'STARTSEITE DER WEBSEITE IN TEXTFORM:'.PHP_EOL;
            $prompt .= $websiteFullText;

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer Local SEO Scoping Experte und sollst ein kritisches, ausführliches und abstraktes Local SEO Scoping ohne Handlungsempfehlungen anhand des gegebenen Kontexts auf deutsch, erwähne 1-2 relevante Beispiele, google Vertrauen bzgl. konsistenter Daten. Ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Local SEO Scoping kritisch ist, Geotargeting der URL-Struktur nicht erwähnen. Kein Fazit. HTML für Formatierungen inkl. einer h1' );


            if( $text ){
                $this->saveText( $scopingId , 'local_seo_text_1' ,$text );
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte local_seo_text_1 nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'local_seo_text_1' ) )
            return;

        if( ! $this->isTextWritten( $scoping , 'local_seo_text_2' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'EINGANGSTEXT LOKALES SEO:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'local_seo_text_1');

            $prompt .= 'RANKING für Suchbegriff "Zahnarzt" an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            $prompt .= print_r($rankingPositions, true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer Local SEO Scoping Experte und sollst eine kritische Ranking-Analyse erstellen, verweise dazu auch auf eine eingeblendete Map auf denen die Ranking Punkte im Radius von circa 500m um den Standort herum verteilt sind. Ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Local SEO Scoping kritisch ist! Kein Fazit. HTML für Formatierungen inkl. einer h. keine Map einbinden. strong mit leerzeichen danach verwenden' );

            if( $text ){
                $this->saveText( $scopingId , 'local_seo_text_2' ,$text );
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte local_seo_text_2 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'local_seo_text_2' ) )
            return;

        if( ! $this->isTextWritten( $scoping , 'seo_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'HTML DER WEBSEITE:'.PHP_EOL;
            $prompt .= $websiteHtml;

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer SEO Scoping Experte und sollst eine kritische SEO-Analyse erstellen, ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist! Kein Fazit. HTML für Formatierungen, strong mit leerzeichen danach verwenden' );

            if( $text ){
                $this->saveText( $scopingId , 'seo_text_1' ,$text );
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte seo_text_1 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'seo_text_1' ) )
            return;


        //$this->saveText( $scopingId , 'page_speed_text_1' ,'' );

        if( ! $this->isTextWritten( $scoping , 'page_speed_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'KONTEXT Onpage-SEO und Webseite:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'seo_text_1' );

            $prompt .= 'PERFORMANCE DATEN:'.PHP_EOL;
            $prompt .= print_r($this->getPageSpeedData($scopingId, false), true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer SEO Webseiten Performance Scoping Experte und sollst eine kurze kritische Performance Analyse erstellen verwende dazu die PERFORMANCE DATEN, ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist! Kein Fazit. HTML für Formatierungen, strong mit leerzeichen danach verwenden. Maximal 900 Zeichen!' );

            if( $text ){
                $this->saveText( $scopingId , 'page_speed_text_1' ,$text );
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte page_speed_text_1 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'page_speed_text_1' ) )
            return;

        if( ! $this->isTextWritten( $scoping , 'ai_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'KONTEXT Onpage-SEO und Webseite:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'seo_text_1' );

            $prompt .= 'KONTEXT Onpage-SEO und Webseite:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'seo_text_1' );

            $prompt .= 'PERFORMANCE DATEN:'.PHP_EOL;
            $prompt .= print_r($this->getPageSpeedData($scopingId, false), true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer SEO Webseiten KI Scoping Experte und sollst eine kurze kritische KI Analyse erstellen mit Einleitung warum KI SUCHE in Zukunft wichtig wird. verwende dazu die KONTEXT DATEN, ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist! Kein Fazit. HTML für Formatierungen, strong mit leerzeichen danach verwenden. Maximal 1600 Zeichen!' );

            if( $text ){
                $this->saveText( $scopingId , 'page_speed_text_1' ,$text );
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte page_speed_text_1 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'page_speed_text_1' ) )
            return;

    }

    private function handleDfsBusinessDataByScoping( $scoping )
    {
        $tag = 'business_data_scoping_' . $scoping['id'];
        $progress = 'is_dfs_business_data_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $task = $this->dfsManager->findTaskByTag( $tag );

        if( $task && $task['status'] == 'completed' ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }else if( $task && $task['status'] == 'failed' ) {
            $this->setStatus($scoping['id'] , 'failed', 'DFS Business Data Task Fehler!' );
        }

        //Wenn bereits vorhanden, dann abbrechen:
        if( $task )
            return;

        $project = $this->projectManager->findProjectById( $scoping['project_id'] );

        if( ! $project || ! isset($project['info_data']['name']) || ! isset($project['info_data']['city']) ) {
            $this->setStatus($scoping['id'] , 'error', 'Projekt oder Projekt info data nicht gefunden!' );
            return;
        }

        //echo $project['info_data']['name'] . ', ' . $project['info_data']['zip']. ' '. $project['info_data']['city'];
        //exit;

        $this->dfsManager->addTask(
            [
                'keyword' => $project['info_data']['name'] . ', ' . $project['info_data']['zip']. ' '. $project['info_data']['city'], //"Zahnarzt",
                //'location_name' =>  $project['info_data']['city'] . ',' .'Germany', //
                //'language_code' => "de",
                "location_code" => 2276,
                'language_name' => 'German',
                'device' => "mobile",
                'tag' => $tag,
                'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "v3/business_data/google/my_business_info/task_post", '' );

    }

    private function handleDfsCompetitorDataByScoping( $scoping )
    {
        $tag = 'business_competitor_scoping_' . $scoping['id'];
        $progress = 'is_dfs_competitor_data_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $task = $this->dfsManager->findTaskByTag( $tag );

        if( $task && $task['status'] == 'completed' ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }else if( $task && $task['status'] == 'failed' ) {
            $this->setStatus($scoping['id'] , 'failed', 'DFS Competitor Task Fehler!' );
        }

        //Wenn bereits vorhanden, dann abbrechen:
        if( $task )
            return;

        $businessData = $this->dfsManager->findTaskByTag( 'business_data_scoping_' . $scoping['id'] );

        //echo '<pre>';
        //print_r( $businessData );
        //exit;

        $businessItem = $businessData['result_data'][0]['items'][0];

        if( ! $businessData || ! $businessItem ) {
            $this->setStatus($scoping['id'] , 'failed', 'DFS Competitor Task Fehler 2!' );
            return;
        }

        $this->dfsManager->addTask(
            [
                'keyword' => 'Zahnarzt', //"Zahnarzt",
                'language_code' => "de",
                'location_coordinate' => $businessItem['latitude'] . ', '.$businessItem['longitude'],
                'device' => "mobile",
                'tag' => $tag,
                //'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "/v3/serp/google/local_finder/task_post", 'advanced' );
    }

    private function handleAddCompetitors( $scoping )
    {
        $progress = 'is_add_competitor_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $businessData   = $this->dfsManager->findTaskByTag('business_data_scoping_' . $scoping['id']);
        $competitorData = $this->dfsManager->findTaskByTag('business_competitor_scoping_' . $scoping['id']);

        if( ! isset($businessData['result_data'][0]['items'][0])) {
            //error
            return;
        }

        $businessItem = $businessData['result_data'][0]['items'][0];

        //echo '<pre>';
        //print_r( $businessItem );
        //exit;

        if( ! isset($competitorData['result_data'][0]['items'])) {
            return;
        }

        $competitors = $competitorData['result_data'][0]['items'];

        $addedIndex = 0;
        foreach ( $competitors as $competitor ) {

            if( $addedIndex >= 4 )
                break;

            if( $businessItem['url'] == $competitor['url'] )
                continue;

            if( ! $competitor['url'] )
                continue;

            $this->projectManager->addCompetitorProject( $competitor['url'] , $scoping['project_id'] );

            $addedIndex++;
        }

        $this->updateProgress( $scoping['id'] , $progress , 1 );

    }

    private function handleRankingPositions( $scoping )
    {
        $scopingId = $scoping['id'];
        $progress = 'is_handle_ranking_positions_finished';

        $positions = $this->getPositions();

        $completedIndex = 0;
        foreach ( $positions as $position ){

            $tag = 'ranking_scoping_'.$position.'_' . $scopingId;

            $task = $this->dfsManager->findTaskByTag( $tag );

            if( $task && $task['status'] == 'completed' ) {
                $completedIndex++;
            }else if( $task && $task['status'] == 'failed' ) {
                $this->setStatus($scoping['id'] , 'failed', 'DFS Ranking Position Fehler!' );
            }
        }

        if( $completedIndex == 4 ){
            $this->updateProgress( $scoping['id'] , $progress , 1 );
        }

        //rankings erhalten an versch. Punkten
        $businessData = $this->getBusinessData( $scoping['id'] );

        $bounding = $this->getBoundingBox( $businessData['latitude'] , $businessData['longitude'], 500);

        $this->fetchRankingPosition( $scopingId , 'top-left' , $bounding['topLeft']['lat'] , $bounding['topLeft']['lon'] );
        $this->fetchRankingPosition( $scopingId , 'top-right' , $bounding['topRight']['lat'] , $bounding['topRight']['lon'] );
        $this->fetchRankingPosition( $scopingId , 'bottom-left' , $bounding['bottomLeft']['lat'] , $bounding['bottomLeft']['lon'] );
        $this->fetchRankingPosition( $scopingId , 'bottom-right' , $bounding['bottomRight']['lat'] , $bounding['bottomRight']['lon'] );
    }

    private function handlePageSpeed($scoping)
    {
        $progress = 'is_page_speed_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $competitors = $this->projectManager->getCompetitorProjects( $scoping['project_id'] );

        $pageSpeedManager = new PageSpeedManager();
        $projectPageSpeed = $pageSpeedManager->getLastEntry( $scoping['project_id'] );

        //get last entries of competitors and check if is_finished
        $projectCount = count($competitors) + 1;
        $finishedCount = 0;

        if( $projectPageSpeed && $projectPageSpeed['is_finished'] ) {
            $finishedCount++;
        }

        foreach( $competitors as $competitor ) {

            $pageSpeedCompetitor = $pageSpeedManager->getLastEntry( $competitor['id'] );

            if( $pageSpeedCompetitor && $pageSpeedCompetitor['is_finished'] ) {
                $finishedCount++;
            }
        }

        if( $projectCount == $finishedCount ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }

        //Anlegen wenn noch nicht vorhanden:
        if( ! $projectPageSpeed ) {
            $pageSpeedManager->addEntry( $scoping['project_id'] );
        }

        foreach( $competitors as $competitor ) {

            if( ! $pageSpeedManager->getLastEntry( $competitor['id'] ) ) {
                $pageSpeedManager->addEntry( $competitor['id'] );
            }
        }
    }

    //GENERATE #####################################



    //HELPER #######################################

    private function getPageSpeedData($scopingId, $isIncludeMobileMetrics = true)
    {
        $scoping = $this->findScopingById($scopingId);
        $project = $this->findProjectById($scoping['project_id']);

        $pagespeedManager = new PageSpeedManager();
        //$pagespeedManager

        $competitors = $this->projectManager->findCompetitorsByProjectId( $scoping['project_id'] );
        
        $output = [];
        $main = [
            'name' => $project['info_data']['name'] .' | '.$this->cleanUrl( $project['website_url'] ),
            'mobile_performance' => $pagespeedManager->getLastMobileScore( $project['id'] ),
            'desktop_performance' => $pagespeedManager->getLastDesktopScore( $project['id'] ),
            'key_metrics_mobile' => $pagespeedManager->getLastKeyMetrics( $project['id'], 'mobile' )
        ];

        if($isIncludeMobileMetrics) {
            $main['key_metrics_mobile'] = $pagespeedManager->getLastKeyMetrics( $project['id'], 'mobile' );
        }

        $output[] = $main;

        $competitorIndex = 1;
        foreach($competitors as $competitor) {
            $entry = [
                'name' => 'Wettbewerber '.$competitorIndex . ' | '.$this->cleanUrl( $competitor['website_url'] ),
                'mobile_performance' => $pagespeedManager->getLastMobileScore( $competitor['id'] ),
                'desktop_performance' => $pagespeedManager->getLastDesktopScore( $competitor['id'] ),
            ];

            if($isIncludeMobileMetrics) {
                $entry['key_metrics_mobile'] = $pagespeedManager->getLastKeyMetrics( $competitor['id'], 'mobile' );
            }

            $output[] = $entry;

            $competitorIndex++;
        }
        //echo '<pre>';
        //print_r($output);
        //exit;

        return $output;

    }

    function cleanUrl($url) {
        $url = preg_replace('#^https?://#', '', $url); // remove http:// or https://
        $url = trim($url, '/'); // remove leading/trailing slashes
        return $url;
    }

    private function saveText($scopingId, $key, $text)
    {
        $this->dbManager->updateMergeData( $scopingId , 'scopings' , 'text_data' , [ $key => $text ] );
    }

    private function getText($scopingId, $key)
    {
        $entry = $this->dbManager->findById( $scopingId ,  'scopings' );

        if( isset( $entry['text_data'][$key]) ) {
            return $entry['text_data'][$key];
        }

        return null;
    }

    private function getPositions()
    {
        $positions = [];
        $positions[] = 'top-left';
        $positions[] = 'top-right';
        $positions[] = 'bottom-left';
        $positions[] = 'bottom-right';

        return $positions;
    }

    private function updateWebsiteHtml($scopingId, $websiteHtml)
    {
        $this->dbManager->updateField($scopingId , 'scopings', 'website_html', $websiteHtml );
    }

    private function isTextWritten($scoping, $key)
    {
        $scoping = $this->findScopingById($scoping['id']);

        if( ! $scoping['text_data'] )
            return false;

        if( ! $scoping['text_data'][$key] )
            return false;

        return true;
    }

    /**
     * Gibt alle Items zurück
     * @param $scopingId
     * @param $position
     * @return |null
     */
    private function getRankingPositionByTag( $scopingId , $tag )
    {

        $localData      = $this->dfsManager->findTaskByTag ( $tag );
        $businessData   = $this->getBusinessData($scopingId);

        if( ! isset($localData['result_data'][0]['items']))
            return null;

        if( ! $businessData )
            return null;

        $localItems = $localData['result_data'][0]['items'];

        foreach ( $localItems as $localItem ) {

            if( $businessData['url'] == $localItem['url'] ) {
                return $localItem['rank_absolute'];
            }
        }

        return 'not ranked';
    }

    /**
     * Gibt alle Items zurück
     * @param $scopingId
     * @param $position
     * @return |null
     */
    private function getRankingPositionDetailsByTag( $scopingId , $tag )
    {

        $localData      = $this->dfsManager->findTaskByTag ( $tag );
        $businessData   = $this->getBusinessData($scopingId);

        $callResult     = json_decode($localData['call_result'], true);
        $coordinates    = explode(',',$callResult['tasks'][0]['data']['location_coordinate']);



        if( ! isset($localData['result_data'][0]['items']))
            return null;

        if( ! $businessData )
            return null;

        $localItems = $localData['result_data'][0]['items'];

        foreach ( $localItems as $localItem ) {

            if( $businessData['url'] == $localItem['url'] ) {
                return [
                    'rank_absolute' => $localItem['rank_absolute'],
                    'lat' => trim($coordinates[0]),
                    'lon' => trim($coordinates[1])
                ];
            }
        }

        return 'not ranked';
    }

    private function getRatingsDataByTag( $scopingId , $tag )
    {

        $localData      = $this->dfsManager->findTaskByTag ( $tag );
        $businessData   = $this->getBusinessData($scopingId);

        if( ! isset($localData['result_data'][0]['items']))
            return null;

        if( ! $businessData )
            return null;

        $localItems = $localData['result_data'][0]['items'];

        return $localItems;
    }

    private function getBusinessData( $scopingId )
    {
        $businessData   = $this->dfsManager->findTaskByTag('business_data_scoping_' . $scopingId );

        if( ! isset($businessData['result_data'][0]['items'][0])) {
            //error
            return null;
        }

        $businessItem = $businessData['result_data'][0]['items'][0];

        return $businessItem;

    }

    private function setStatus( $scopingId , $status, $statusInfo = '' )
    {
        $this->dbManager->updateField($scopingId , 'scopings' , 'status', $status );

        if( $statusInfo ) {
            $this->dbManager->updateField($scopingId , 'scopings' , 'status_info', $statusInfo );
        }
    }

    private function findProjectById( $projectId )
    {
        return $this->projectManager->findProjectById( $projectId );
    }

    private function isProgressStepFinished( $scopingId , $stepName )
    {
        $scoping = $this->findScopingById( $scopingId );

        if( ! $scoping )
            return null;

        if( isset($scoping['progress_data'][$stepName]) && $scoping['progress_data'][$stepName] )
            return true;

        return false;
    }

    private function updateProgress( $scopingId, $key , $value)
    {
        $this->dbManager->updateMergeData( $scopingId , 'scopings' , 'progress_data', [ $key => $value ] );
    }


    /**
     * Berechnet die Koordinaten eines umschließenden Rechtecks (Bounding Box)
     * um einen gegebenen Mittelpunkt mit einer bestimmten Entfernung zu den Seiten.
     *
     * @param float $centerLat Breitengrad des Mittelpunkts in Dezimalgrad.
     * @param float $centerLon Längengrad des Mittelpunkts in Dezimalgrad.
     * @param float $distanceM Entfernung vom Mittelpunkt zu jeder der vier Seiten des Rechtecks in Metern.
     *                         Das resultierende Quadrat hat also eine Seitenlänge von 2 * $distanceM.
     * @return array Ein assoziatives Array mit vier Punkten: 'topLeft', 'topRight', 'bottomLeft', 'bottomRight'.
     *               Jeder Punkt ist ein assoziatives Array mit 'lat' und 'lon'.
     *               Gibt false zurück bei ungültigen Eingaben (z.B. negative Distanz).
     */
    private function getBoundingBox(float $centerLat, float $centerLon, float $distanceM)
    {
        if ($distanceM < 0) {
            // Oder eine Exception werfen: throw new InvalidArgumentException("Distance must be non-negative.");
            return false;
        }
        if ($centerLat < -90 || $centerLat > 90 || $centerLon < -180 || $centerLon > 180) {
            // Oder eine Exception werfen
            return false;
        }

        // Erdradius in Metern (Durchschnittswert)
        // Für höhere Genauigkeit könnte man das WGS84 Ellipsoid verwenden,
        // aber für die meisten Anwendungsfälle ist ein Kugelmodell ausreichend.
        $earthRadiusM = 6371000.0;

        // Umrechnung des Breitengrads in Radiant für trigonometrische Funktionen
        $centerLatRad = deg2rad($centerLat);

        // Berechnung der Änderung im Breitengrad (delta Latitude)
        // Die Distanz pro Breitengrad ist relativ konstant.
        // angular_distance = distance / radius
        $deltaLatRad = $distanceM / $earthRadiusM;
        $deltaLatDeg = rad2deg($deltaLatRad);

        // Berechnung der Änderung im Längengrad (delta Longitude)
        // Die Distanz pro Längengrad hängt vom Breitengrad ab (konvergiert zu den Polen).
        // distance_lon = radius_at_latitude * delta_lon_rad
        // radius_at_latitude = earth_radius * cos(center_lat_rad)
        // delta_lon_rad = distance_lon / (earth_radius * cos(center_lat_rad))

        // Vermeide Division durch Null an den Polen (cos(pi/2) = 0)
        $cosLat = cos($centerLatRad);
        if (abs($cosLat) < 1e-9) { // Nahe oder an einem Pol
            // An den Polen selbst ist ein "Quadrat" in Längengraden nicht wohldefiniert.
            // Man könnte hier deltaLonDeg auf 180 setzen, um alle Längengrade abzudecken,
            // oder einen Fehler werfen, je nach Anwendungsfall.
            // Für ein kleines Quadrat "um" den Pol werden die Längengrade sehr stark gedehnt.
            // Ein deltaLonDeg von 180 bedeutet, dass die Box von -180 bis +180 Längengrad reicht.
            $deltaLonDeg = 180.0;
        } else {
            $deltaLonRad = $distanceM / ($earthRadiusM * $cosLat);
            $deltaLonDeg = rad2deg($deltaLonRad);
        }

        // Berechne die Koordinaten der Eckpunkte
        // Norden ist +Lat, Süden ist -Lat
        // Osten ist +Lon, Westen ist -Lon

        $points = [
            'topLeft' => [
                'lat' => $centerLat + $deltaLatDeg,
                'lon' => $centerLon - $deltaLonDeg
            ],
            'topRight' => [
                'lat' => $centerLat + $deltaLatDeg,
                'lon' => $centerLon + $deltaLonDeg
            ],
            'bottomLeft' => [
                'lat' => $centerLat - $deltaLatDeg,
                'lon' => $centerLon - $deltaLonDeg
            ],
            'bottomRight' => [
                'lat' => $centerLat - $deltaLatDeg,
                'lon' => $centerLon + $deltaLonDeg
            ]
        ];

        // Optionale Normalisierung der Koordinaten:
        // Breitengrade auf [-90, 90] beschränken.
        // Längengrade auf [-180, 180] wickeln (modulo).
        // Dies ist oft nicht nötig, da viele Kartenbibliotheken dies intern handhaben
        // oder Koordinaten außerhalb des Bereichs korrekt interpretieren.
        // Beispiel für Normalisierung (optional einfügen):
        /*
        foreach ($points as &$point) {
            $point['lat'] = max(-90.0, min(90.0, $point['lat']));
            // Längengrad Normalisierung: ($lon + 180) % 360 - 180
            // Vorsicht mit fmod bei negativen Zahlen, ggf. eigene Funktion
            while ($point['lon'] <= -180) {
                $point['lon'] += 360;
            }
            while ($point['lon'] > 180) {
                $point['lon'] -= 360;
            }
        }
        unset($point); // Referenz aufheben
        */

        return $points;
    }

    private function fetchRankingPosition( $scopingId , $position , $lat , $lon )
    {
        $tag = 'ranking_scoping_'.$position.'_' . $scopingId;

        //Wenn bereits vorhanden, dann abbrechen:
        $task = $this->dfsManager->findTaskByTag( $tag );
        if( $task )
            return;

        $this->dfsManager->addTask(
            [
                'keyword' => 'Zahnarzt', //"Zahnarzt",
                'language_code' => "de",
                'location_coordinate' => $lat . ', ' . $lon,
                'device' => "mobile",
                'tag' => $tag,
                //'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "/v3/serp/google/local_finder/task_post", 'advanced' );

    }


}
