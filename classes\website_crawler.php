<?php

class WebsiteCrawler
{
    // <PERSON><PERSON><PERSON><PERSON><PERSON> von private zu protected
    protected $url;
    protected $html;
    protected $title;
    protected $metaDescription;
    protected $headlines;
    protected $altTags;
    protected $invalidAltCount;
    protected $internalLinks;
    protected $statusCode;

    public function __construct($url = null, $domain = null)
    {
        if (!$url) {
            return;
        }
        $this->url = $this->buildValidUrl($url, $domain);
    }

    public function getUrl()
    {
        return $this->url;
    }

    public function fetch()
    {
        $this->fetchHtml(); // Wird die Methode der Kindklasse aufrufen, falls überschrieben
        $this->parseContent(); // Wird die Methode der Elternklasse aufrufen (oder Kindklasse, falls überschrieben)
    }

    public function getStatusCode()
    {
        return $this->statusCode;
    }

    public function getFullText(): string
    {
        if (empty($this->html)) {
            return '';
        }

        $cleanHtml = $this->html;

        // 1. Remove <script> and <style> tags and their content
        // These often contain non-displayable text or code.
        $cleanHtml = preg_replace(
            [
                '#<script\b[^>]*>(.*?)</script>#is', // Added # delimiters for clarity
                '#<style\b[^>]*>(.*?)</style>#is'
            ],
            '',
            $cleanHtml
        );

        // 2. Pre-processing to prevent words from glueing together:
        // 2a. Convert line break tags (<br>, <hr>) to a single space.
        // This ensures they act as word separators.
        $cleanHtml = preg_replace('#<(br|hr)\s*/?>#i', ' ', $cleanHtml);

        // 2b. Insert a space between adjacent tags.
        // E.g., "</div><p>" becomes "</div> <p>".
        // E.g., "</span><span>" becomes "</span> <span>".
        // This helps separate text content that was in different tags.
        $cleanHtml = preg_replace('#></#', '> <', $cleanHtml);

        // 3. Remove all remaining HTML tags.
        // Because of step 2, this is now safer regarding glued words.
        $text = strip_tags($cleanHtml);

        // 4. Decode HTML entities to their actual characters.
        // E.g., & becomes &,   becomes a space.
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // 5. Normalize whitespace.
        // Replace multiple spaces, tabs, newlines, etc., with a single space.
        // The 'u' modifier ensures correct handling of Unicode whitespace.
        $text = preg_replace('/\s+/u', ' ', $text);

        // 6. Trim leading and trailing whitespace.
        return trim($text);
    }

    public function getHtml()
    {
        return $this->html;
    }

    public function setHtml($html)
    {
        $this->html = $html;
    }

    public function buildValidUrl($url, $domain = null)
    {
        if (!$url) {
            return '';
        }

        if (empty(parse_url($url, PHP_URL_HOST))) {
            if ($domain) {
                if (!preg_match('/^https?:\/\//i', $domain)) {
                    $domain = 'https://' . $domain;
                }
                $domain = rtrim($domain, '/');
                if ($url[0] !== '/') {
                    $url = '/' . $url;
                }
                $url = $domain . $url;
            }
        } else {
            if (!preg_match('/^https?:\/\//i', $url)) {
                $url = 'https://' . $url;
            }
        }

        return $url;
    }

    // Geändert von private zu protected
    protected function fetchHtml()
    {
        if (!$this->url) {
            $this->html = false;
            $this->statusCode = null; // Oder einen spezifischen Fehlercode
            return;
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Vorsicht im Produktivbetrieb
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // Vorsicht im Produktivbetrieb
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            // Optional: Log error $error
            $this->html = false;
            $this->statusCode = null; // Oder einen spezifischen Fehlercode für curl-Fehler
        } else {
            $this->html = $response;
            $this->statusCode = $httpCode;
        }
    }

    // Geändert von private zu protected
    protected function parseContent()
    {
        // Reset properties before parsing
        $this->title = '';
        $this->metaDescription = '';
        $this->headlines = [];
        $this->altTags = [];
        $this->invalidAltCount = 0;
        $this->internalLinks = [];

        if ($this->html === false || empty($this->html) || $this->statusCode >= 400) {
            // Do not attempt to parse if fetching failed or resulted in an error status code
            return;
        }

        $dom = new DOMDocument();
        libxml_use_internal_errors(true); // Suppress HTML5 parsing errors
        // Explicitly specify UTF-8 encoding if possible
        // Prepending the encoding declaration can help DOMDocument
        //$encodedHtml = mb_convert_encoding($this->html, 'HTML-ENTITIES', 'UTF-8');
        $dom->loadHTML($this->html, LIBXML_NOWARNING | LIBXML_NOERROR);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);

        $titleNodes = $dom->getElementsByTagName('title');
        if ($titleNodes->length > 0) {
            $this->title = trim($titleNodes->item(0)->nodeValue);
        }

        $metaNodes = $xpath->query('//meta[@name="description"]');
        if ($metaNodes->length > 0) {
            $this->metaDescription = trim($metaNodes->item(0)->getAttribute('content'));
        } elseif (($metaNodes = $xpath->query('//meta[@name="Description"]')) && $metaNodes->length > 0) {
            // Fallback for capitalized "Description"
            $this->metaDescription = trim($metaNodes->item(0)->getAttribute('content'));
        }


        $headlineNodes = $xpath->query('//h1 | //h2 | //h3 | //h4 | //h5 | //h6');
        foreach ($headlineNodes as $node) {
            $htmlHeadline = trim($dom->saveHTML($node));
            if (!empty($htmlHeadline)) {
                $this->headlines[] = $htmlHeadline;
            }
        }

        $minAltLength = 5; // Min length for a "valid" alt tag
        $imgNodesAll = $xpath->query('//img');
        foreach ($imgNodesAll as $img) {
            $alt = null;
            if ($img->hasAttribute('alt')) {
                $alt = trim($img->getAttribute('alt'));
            }

            // Count as invalid if alt attribute is missing OR the alt text is shorter than min length
            if ($alt === null || mb_strlen($alt) < $minAltLength) {
                $this->invalidAltCount++;
            } else {
                $this->altTags[] = $alt;
            }
        }

        $linkNodes = $xpath->query('//a[@href]');
        $baseUrl = $this->url; // Base URL for resolving relative links
        $baseHost = parse_url($baseUrl, PHP_URL_HOST);

        foreach ($linkNodes as $link) {
            $href = trim($link->getAttribute('href'));
            if ($this->isInternalLink($href, $baseHost)) {
                $linkText = trim(preg_replace('/\s+/', ' ', $link->nodeValue)); // Normalize whitespace in link text
                if (empty($linkText)) {
                    // Attempt to get alt text from nested img if link text is empty
                    $imgInLink = $xpath->query('.//img[@alt]', $link);
                    if ($imgInLink->length > 0) {
                        $linkText = trim($imgInLink->item(0)->getAttribute('alt'));
                    }
                }
                if (empty($linkText)) {
                    // Fallback if still no text found
                    $linkText = $href;
                }

                // Optionally resolve relative URLs to absolute ones before storing
                $absoluteHref = $this->resolveUrl($href, $baseUrl);

                $this->internalLinks[] = $linkText . ': ' . $absoluteHref; // Store with absolute URL
            }
        }
        // Remove duplicates that might occur due to resolution
        $this->internalLinks = array_unique($this->internalLinks);
    }

    protected function isInternalLink(string $href, string $baseHost): bool
    {
        if (
            empty($href) ||
            $href[0] === '#' ||
            stripos($href, 'mailto:') === 0 ||
            stripos($href, 'tel:') === 0 ||
            stripos($href, 'javascript:') === 0
        ) {
            return false;
        }

        $linkHost = parse_url($href, PHP_URL_HOST);
        if (empty($linkHost)) {
            // relative URL → internal
            return true;
        }

        // normalize both hosts: lowercase + strip leading "www."
        $linkHostNorm = preg_replace('/^www\./i', '', strtolower($linkHost));
        $baseHostNorm = preg_replace('/^www\./i', '', strtolower($baseHost));

        return $linkHostNorm === $baseHostNorm;
    }


    // Helper function to resolve relative URLs (optional but useful)
    protected function resolveUrl($relativeUrl, $baseUrl) {
        if (strpos($relativeUrl, '://') !== false) {
            return $relativeUrl; // Already absolute
        }

        $baseParts = parse_url($baseUrl);
        if (!isset($baseParts['scheme']) || !isset($baseParts['host'])) {
            return $relativeUrl; // Cannot resolve without base scheme/host
        }

        $scheme = $baseParts['scheme'];
        $host = $baseParts['host'];
        $port = isset($baseParts['port']) ? ':' . $baseParts['port'] : '';
        $path = isset($baseParts['path']) ? $baseParts['path'] : '/';

        if ($relativeUrl[0] === '/') {
            if (isset($relativeUrl[1]) && $relativeUrl[1] === '/') {
                // Scheme-relative URL (e.g., //example.com/path)
                return $scheme . ':' . $relativeUrl;
            } else {
                // Absolute path (e.g., /path/to/file)
                return $scheme . '://' . $host . $port . $relativeUrl;
            }
        }

        // Relative path (e.g., path/to/file or ../../file)
        // Remove filename from base path if present
        $basePathDir = dirname($path);
        if ($basePathDir === '.') $basePathDir = ''; // Handle case where path is just "/" or filename
        if ($basePathDir === '/') $basePathDir = ''; // Avoid double slash later

        // Build absolute path from relative path parts
        $absolutePath = $basePathDir . '/' . $relativeUrl;

        // Resolve '/./' and '/../' segments
        $parts = explode('/', $absolutePath);
        $resolvedParts = [];
        foreach ($parts as $part) {
            if ($part === '.' || $part === '') {
                continue;
            }
            if ($part === '..') {
                array_pop($resolvedParts);
            } else {
                $resolvedParts[] = $part;
            }
        }

        return $scheme . '://' . $host . $port . '/' . implode('/', $resolvedParts);
    }


    public function getTitle()
    {
        return $this->title;
    }

    public function getMetaDescription()
    {
        return $this->metaDescription;
    }

    public function getHeadlines()
    {
        return $this->headlines;
    }

    public function getAltTags()
    {
        return $this->altTags;
    }

    public function getInvalidAltCount()
    {
        return $this->invalidAltCount;
    }

    public function getInternalLinks()
    {
        return $this->internalLinks;
    }

    public function getSeoSummary()
    {
        return [
            'url' => $this->url,
            'statusCode' => $this->statusCode,
            'title' => $this->title,
            'metaDescription' => $this->metaDescription,
            'headlines' => $this->headlines,
            'altTags' => $this->altTags,
            'invalidAltCount' => $this->invalidAltCount,
            'internalLinks' => $this->internalLinks
        ];
    }

    public function getSeoSummaryAsText()
    {
        $summary = "URL: " . ($this->url ?: 'N/A') . "\n";
        $summary .= "Status Code: " . ($this->statusCode ?: 'N/A') . "\n\n";
        $summary .= "Title:\n" . ($this->title ?: 'Kein Title gefunden') . "\n\n";
        $summary .= "Meta Description:\n" . ($this->metaDescription ?: 'Keine Meta Description gefunden') . "\n\n";
        $summary .= "Überschriften (HTML):\n" . (!empty($this->headlines) ? implode("\n", $this->headlines) : 'Keine Überschrift gefunden') . "\n\n";
        $summary .= "Bild Alt-Tags (>= " . $this->getMinAltLengthForSummary() . " Zeichen):\n" . (!empty($this->altTags) ? implode("\n", $this->altTags) : 'Keine gültigen Alt-Tags gefunden') . "\n\n";
        $summary .= "Bilder mit fehlendem oder zu kurzem alt-Tag: {$this->invalidAltCount}\n\n";
        $summary .= "Interne Links (Text: URL):\n" . (!empty($this->internalLinks) ? implode("\n", $this->internalLinks) : 'Keine internen Links gefunden');
        return $summary;
    }

    // Helper to get the min alt length used in parseContent for the summary text
    private function getMinAltLengthForSummary() {
        // Ideally, this would be a constant or protected property if it needs to be configurable/accessible
        return 5;
    }

}