# Google Maps Integration Setup

This document explains how to set up Google Maps integration for the Local SEO scoping templates.

## Overview

The Local SEO template (`local_seo_1.php`) includes an interactive Google Map that displays ranking positions at different geographic points around a business location. The map shows 5 points with color-coded markers based on ranking positions (1-10).

## Color Coding

- **Green (#00ff00)**: Positions 1-2 (Very good)
- **<PERSON> Green (#80ff00)**: Positions 3-4 (Good)  
- **Yellow (#ffff00)**: Positions 5-6 (Average)
- **Orange (#ff8000)**: Positions 7-8 (Poor)
- **Red (#ff0000)**: Positions 9-10 (Very poor)

## Setup Instructions

### 1. Get a Google Maps API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the "Maps JavaScript API"
4. Go to "Credentials" and create an API key
5. Restrict the API key to your domain for security

### 2. Configure the API Key

You have several options to configure the API key:

#### Option A: Configuration File (Recommended)
1. Copy `config/google_maps.php.example` to `config/google_maps.php`
2. Edit the file and replace `YOUR_GOOGLE_MAPS_API_KEY_HERE` with your actual API key

#### Option B: Environment Variable
Set the environment variable `GOOGLE_MAPS_API_KEY` with your API key

#### Option C: PHP Constant
Define the constant `GOOGLE_MAPS_API_KEY` in your application configuration

### 3. API Key Restrictions (Security)

For security, restrict your API key:

1. **Application restrictions**: Add your domain(s)
2. **API restrictions**: Limit to "Maps JavaScript API"

Example domain restrictions:
- `yourdomain.com/*`
- `*.yourdomain.com/*`
- `localhost/*` (for development)

## Features

### Interactive Map
- Displays 5 ranking points around the business location
- Color-coded markers based on ranking positions
- Clickable markers with detailed information
- 2km radius circle showing the analysis area

### Fallback Display
If the Google Maps API is not available or configured:
- Shows a static map representation
- Displays all ranking positions with color coding
- Maintains visual consistency with the interactive version

### Print Support
- Optimized for PDF generation
- Maintains colors and layout in print mode
- Prevents page breaks within the map area

## Data Integration

The map automatically uses project data when available:

### From Project Data
- Business coordinates (latitude/longitude)
- Company name and address information
- Automatically generates points around the business location

### Default Behavior
- Uses Frankfurt am Main as default center (50.1109, 8.6821)
- Generates sample ranking data for demonstration

## Customization

### Adding More Points
Edit the `mapData.points` array in the template to add more ranking locations.

### Changing Colors
Modify the `getMarkerColor()` function to adjust the color scheme.

### Map Settings
Adjust zoom level, map type, and other settings in the map initialization.

## Troubleshooting

### Map Not Loading
1. Check browser console for API key errors
2. Verify API key is correctly configured
3. Ensure Maps JavaScript API is enabled
4. Check domain restrictions on the API key

### Quota Exceeded
- Monitor your API usage in Google Cloud Console
- Consider implementing usage limits
- Upgrade your billing plan if needed

### Print Issues
- The map automatically switches to a static representation for printing
- Ensure print styles are not being overridden

## Development Notes

- The map loads asynchronously to avoid blocking page rendering
- Includes error handling for API failures
- Supports both development and production environments
- Gracefully degrades when API is unavailable
