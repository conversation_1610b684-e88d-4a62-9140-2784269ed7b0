<?php

// Include necessary files
require_once '../app/_base/base.php';
require_once '../classes/scoping_manager.php';

// Create a ScopingManager instance
$scopingManager = new ScopingManager();

echo "=== Scoping Manager Rendered Pages Example ===\n\n";

// Test the getRenderedScopingPages function
echo "Testing getRenderedScopingPages function...\n";

// Get a test scoping ID (use the first available scoping)
$testScopings = $dbManager->findAll('scopings', 'created_at DESC', 1);
if (empty($testScopings)) {
    echo "No scopings found in database. Please create a scoping first.\n";
    exit;
}

$testScopingId = $testScopings[0]['id'];
echo "Using scoping ID: $testScopingId\n\n";

$renderedPages = $scopingManager->getRenderedScopingPages($testScopingId);

if (empty($renderedPages)) {
    echo "No rendered pages found or error occurred.\n";
} else {
    echo "Found " . count($renderedPages) . " rendered pages:\n\n";

    foreach ($renderedPages as $index => $renderedPage) {
        echo "=== Page " . ($index + 1) . " ===\n";
        echo "Length: " . strlen($renderedPage) . " characters\n";
        echo "Preview (first 200 chars):\n";
        echo substr(strip_tags($renderedPage), 0, 200) . "...\n\n";
    }
}

echo "Example completed.\n";
